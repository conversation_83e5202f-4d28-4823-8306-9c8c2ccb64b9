# Type101Sensor_AWS - AWS IoT Core Version

ESP8266 NodeMCU sensor device with AM2320 temperature/humidity sensor, OLED display, and AWS IoT Core connectivity.

## 🎯 **Project Overview**

This is the **AWS IoT Core version** of the Type101Sensor project, based on the working MQTT version but modified for secure AWS IoT connectivity.

### **📋 Features**
- ✅ **ESP8266 NodeMCU v1** compatibility
- ✅ **AM2320 temperature/humidity sensor** 
- ✅ **128x32 OLED display** with status information
- ✅ **WiFi configuration portal** for easy setup
- ✅ **EEPROM configuration storage**
- ✅ **AWS IoT Core connectivity** with TLS/SSL security
- ✅ **Device shadows** for remote configuration
- ✅ **Unique device identification** via chip ID
- ✅ **Remote calibration** via AWS IoT

### **🔧 Hardware Requirements**
- ESP8266 NodeMCU v1 board
- AM2320 temperature/humidity sensor (I2C)
- 128x32 OLED display (SSD1306, I2C)
- Breadboard and jumper wires

### **📡 Pin Connections**
```
NodeMCU v1    | Component
------------- | ---------
D1 (GPIO5)    | I2C SCL (AM2320 & OLED)
D2 (GPIO4)    | I2C SDA (AM2320 & OLED)
A0            | Analog sensor input
3V3           | VCC (AM2320 & OLED)
GND           | GND (AM2320 & OLED)
```

### **🚀 Getting Started**

#### **1. AWS IoT Setup**
Before using this code, you need to:
1. Create an AWS IoT Thing in the AWS Console
2. Generate device certificates
3. Create IoT policies
4. Replace the certificate placeholders in `main.cpp`

#### **2. PlatformIO Setup**
```bash
# Clone or copy this project
cd Type101Sensor_AWS

# Install dependencies (PlatformIO will handle this)
platformio lib install

# Build the project
platformio run

# Upload to device
platformio run --target upload
```

#### **3. Arduino IDE Setup**
If you prefer Arduino IDE:
1. Install required libraries:
   - MQTT by Joel Gaehwiler
   - Adafruit AM2320 sensor library
   - Adafruit SSD1306
   - Adafruit GFX Library
   - ArduinoJson
2. Replace certificate placeholders with your AWS IoT certificates
3. Update AWS IoT endpoint in the code
4. Upload to your NodeMCU

### **📊 AWS IoT Topics**

The device uses these AWS IoT topics:
- **Data Publishing:** `sensors/mikehagans/Hacienda/barn/DeepFreezer1/{nodeID}/data`
- **Shadow Updates:** `$aws/thing/{thingName}/shadow/update`
- **Shadow Delta:** `$aws/thing/{thingName}/shadow/update/delta`

### **🔒 Security**

This version uses:
- **TLS 1.2** encryption for all communications
- **X.509 certificates** for device authentication
- **AWS IoT policies** for access control
- **Device shadows** for secure configuration updates

### **📈 Data Format**

Sensor data is published in JSON format:
```json
{
  "device_type": 101,
  "node_id": "aB3dE5",
  "fw_version": "v1.0-AWS",
  "friendly_name": "rackroom",
  "timestamp": 1234567890,
  "rssi": -45,
  "values": {
    "temperature_c": 23.5,
    "humidity_percent": 65.2,
    "adc_percent": 78
  }
}
```

### **🔧 Configuration**

Device configuration can be updated via AWS IoT device shadows:
```json
{
  "desired": {
    "adc_calibration": 1.05,
    "temp_offset": -0.5,
    "humidity_offset": 2.0,
    "publish_interval_ms": 60000,
    "friendlyName": "Freezer Sensor"
  }
}
```

### **📝 Version History**

- **v1.0-AWS**: Initial AWS IoT Core version based on MQTT v0.7
- Based on working MQTT version with 937 lines of code
- Added AWS IoT Core connectivity with TLS/SSL
- Added device shadow support
- Embedded icons for standalone operation

### **🔗 Related Projects**

- **Type101Sensor** (Original MQTT version): `../Type101Sensor/`
- **Arduino IDE Version**: Available in `c:\more\arduino sketches\aws101\`

### **⚠️ Important Notes**

1. **Certificates Required**: You must replace the certificate placeholders with your actual AWS IoT certificates
2. **Endpoint Configuration**: Update the AWS IoT endpoint URL for your region
3. **Thing Name**: Ensure your AWS IoT Thing name matches the code configuration
4. **Memory Usage**: AWS IoT requires more memory than basic MQTT due to TLS overhead

### **🆘 Troubleshooting**

- **Connection Issues**: Check certificates and endpoint URL
- **Memory Errors**: Reduce buffer sizes if needed
- **Display Issues**: Verify I2C connections
- **Sensor Errors**: Check AM2320 wiring and power

---

**This project maintains the same hardware compatibility and functionality as the original MQTT version while adding secure AWS IoT Core connectivity.**
