; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:nodemcu]
platform = espressif8266
board = esp12e
framework = arduino
monitor_speed = 115200
upload_port = COM7
upload_speed = 57600
upload_resetmethod = dio

lib_deps =
  knolleary/PubSubClient@^2.8
  adafruit/Adafruit Unified Sensor@^1.1.9
  adafruit/Adafruit AM2320 sensor library@^1.2.5
  adafruit/Adafruit SSD1306@^2.5.7
  adafruit/Adafruit GFX Library@^1.11.9
  bblanchon/Ard<PERSON>o<PERSON><PERSON>@^6.21.2

board_build.flash_mode = dio
