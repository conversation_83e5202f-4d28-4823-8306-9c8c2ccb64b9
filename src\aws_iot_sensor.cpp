// ESP8266 AWS IoT Core Telemetry Publisher
// Connects to AWS IoT Core and publishes sensor data
// Supports AM2320 temperature/humidity sensor, analog input, and digital inputs
// Displays status on SSD1306 OLED display

// === LIBRARIES ===
#include <ESP8266WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_AM2320.h>
#include <Adafruit_SSD1306.h>
#include <Adafruit_GFX.h>
#include <ArduinoJson.h>
#include <WiFiManager.h>
#include <NTPClient.h>
#include <WiFiUdp.h>
#include <EEPROM.h>

// === FIRMWARE & DEVICE INFO ===
const char* FIRMWARE_VERSION = "v1.0";
const int DEVICE_TYPE = 101;

// === PIN CONFIGURATION ===
const int SDA_PIN = D2;           // I2C Data for Sensor/Display
const int SCL_PIN = D1;           // I2C Clock for Sensor/Display
const int DIGITAL_INPUT_1 = D5;   // GPIO14
const int DIGITAL_INPUT_2 = D6;   // GPIO12
// Note: A0 is used implicitly by analogRead(A0)

// === WIFI CONFIGURATION ===
#define WIFI_SSID "HaciendaHagansLR"
#define WIFI_PASS "F1nglongers"
#define WIFI_CONNECT_TIMEOUT 20000  // 20 seconds

// === DISPLAY CONFIGURATION ===
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 32
#define OLED_RESET_PIN -1
#define DISPLAY_UPDATE_INTERVAL_MS 1000

// === AWS IOT CONFIGURATION ===
const char* aws_endpoint = "a2ltfva6jq44zv-ats.iot.us-east-1.amazonaws.com";
const char* certificate_pem_crt = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDWTCCAkGgAwIBAgIUQLLrIO5d6G9m6/tgpMttQs9ncW0wDQYJKoZIhvcNAQEL
BQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g
SW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI1MDYxMzAwNTgx
NVoXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0
ZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKsW39KzechA5NkWWfAF
5H6sNeICnc9hjGjZQ3tI6VSHjDFhGNtnf+2eC6HQZ/m86jSOtmJnvYD0lBd/4cle
ZT/LLiDTRgyFL+GKnmgRN0KhNsvgVaR1tnTMY+AszAhpgBf/AHTgoq57d3mG4y22
jXqa8iZmHm4y1tm60/b44gdHbVEdRko1ntCoaOTKqMOJhIWMMO6EjfawcpNwb3Pu
v5PWDGyqWsPEA9ZJ74WZNVAcZkY0ei9jF/lyzJGiNjEKgduQshac4EjZRchFDCZi
5fYmWVyKXn8DZAWxA03ng6pFI/OajcSvSVGa9tTsLb0H1wVvZqX4x7k3u2F3uoe1
Nd0CAwEAAaNgMF4wHwYDVR0jBBgwFoAUqHWRrqPVtVv1vCaOqsZ0Z00nZ24wHQYD
VR0OBBYEFLgIoaMa8XrekF2uZJEgiGTR5NDJMAwGA1UdEwEB/wQCMAAwDgYDVR0P
AQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCg7mMDsgkI33zT4pBQwC8Yyyxs
uwEuMnZu0ekJdWx7UL2aEXc7zyFNDPYEilE3CF7UyQ3D0yn43vIN+jKNSHVtnVWK
Np1B6aT0x2Cd1PDsi0OOoFuhxkQ1evLbfrEh9ECJOYRd3ll2v0yPOpTDAN/nHEOy
tbbRvbt4lwYQagahz+tJcyv0LsZj5rY/m5bDKRTcWj0IdGKF2Y6fc616AvJf/G3s
kHYSGXsOzJJ4gwENzYiGmIS9m6RmSjrwu/zy2dsJ3VHgqmKgeVOaQEkr1Zz4L47m
MlGKkBLCUcmTfc8v2fUf6YVPXwgLjVpzvZ2OU1u5AVMUPat7r16d2Xcy0O76
-----END CERTIFICATE-----
)EOF";

const char* private_pem_key = R"EOF(
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)EOF";

const char* amazon_ca_cert = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIUJrU6adTrCC2qJeHZERxhlbI1Bjjt/msv0tadQ1wUs
N+gDS63pYaACbvXy8MWy7Vu33PqUXHeeE6V/Uq2V8viTO96LXFvKWlJbYK8U90vv
o/ufQJVtMVT8QtPHRh8jrdkPSHCa2XV4cdFyQzR1bldZwgJcJmApzyMZFo6IQ6XU
5MsI+yMRQ+hDKXJioaldXgjUkK642M4UwtBV8ob2xJNDd2ZhwLnoQdeXeGADbkpy
rqXRfboQnoZsG4q5WTP468SQvvG5
-----END CERTIFICATE-----
)EOF";

// === TIMING & INTERVALS ===
#define DEFAULT_PUBLISH_INTERVAL_MS 60000  // Default: 60 seconds
#define SENSOR_READ_INTERVAL_MS 2000       // How often to read sensors
#define MQTT_RECONNECT_INTERVAL_MS 5000    // Wait between MQTT reconnect attempts

// === OBJECTS ===
WiFiClientSecure wifiClient;
PubSubClient mqttClient(wifiClient);
Adafruit_AM2320 am2320 = Adafruit_AM2320();
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET_PIN);
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, "pool.ntp.org");

// === GLOBAL VARIABLES ===
bool displayOK = false;
char deviceId[16];  // Hex string of ESP8266 Chip ID
unsigned long publishIntervalMs = DEFAULT_PUBLISH_INTERVAL_MS;
String publishTopic;
String configTopic;
String controlTopic;

// State variables for timing and last known values
unsigned long lastPublishTimestamp = 0;
unsigned long lastDisplayUpdateTimestamp = 0;
unsigned long lastSensorUpdateTimestamp = 0;
unsigned long lastMqttAttemptTimestamp = 0;
float lastTempC = NAN;
float lastHumidity = NAN;
float lastVoltage = 0.0;
int lastBinaryInput = 0;
char lastTimestamp[25] = "";  // ISO8601 timestamp buffer

// === FUNCTION IMPLEMENTATIONS ===

// Generate a unique device ID from the ESP8266 chip ID
void generateDeviceId() {
  uint32_t chipId = ESP.getChipId();
  snprintf(deviceId, sizeof(deviceId), "%08X", chipId);
  Serial.print("Device ID: ");
  Serial.println(deviceId);
}

// Setup WiFi connection with fallback to captive portal
void setupWiFi() {
  Serial.println("Setting up WiFi...");
  
  if (displayOK) {
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("Connecting to WiFi...");
    display.display();
  }

  // Try to connect to configured WiFi
  WiFi.begin(WIFI_SSID, WIFI_PASS);
  
  unsigned long startAttemptTime = millis();
  
  while (WiFi.status() != WL_CONNECTED && 
         millis() - startAttemptTime < WIFI_CONNECT_TIMEOUT) {
    delay(500);
    Serial.print(".");
  }
  
  // If connection




