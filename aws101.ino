// NodeMCU12_MQTT_AM2320_OLED_v0.7 (Arduino IDE Version)
// ESP8266 Sensor with AM2320 temp/humidity, OLED display, WiFi config portal, and MQTT

// === LIBRARIES ===
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <DNSServer.h>
#include <PubSubClient.h>
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_AM2320.h>       // AM2320 Temp/Humidity Sensor
#include <Adafruit_SSD1306.h>     // OLED Display Driver
#include <Adafruit_GFX.h>         // Graphics library for display
#include <ArduinoJson.h>          // MQTT Payload Handling
#include <EEPROM.h>               // Configuration Storage

// === ICONS (Embedded) ===
// WiFi Icons (8 bytes each)
const uint8_t wifi_icon_0[] PROGMEM = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}; // No WiFi
const uint8_t wifi_icon_1[] PROGMEM = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00}; // 1 bar
const uint8_t wifi_icon_2[] PROGMEM = {0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x08, 0x00}; // 2 bars
const uint8_t wifi_icon_3[] PROGMEM = {0x00, 0x00, 0x00, 0x3E, 0x00, 0x1C, 0x08, 0x00}; // 3 bars
const uint8_t wifi_icon_4[] PROGMEM = {0x00, 0x7F, 0x00, 0x3E, 0x00, 0x1C, 0x08, 0x00}; // 4 bars

// MQTT Icons (8 bytes each)
const uint8_t mqtt_connected[] PROGMEM = {0x3C, 0x42, 0x81, 0x81, 0x81, 0x81, 0x42, 0x3C}; // Circle (connected)
const uint8_t mqtt_disconnected[] PROGMEM = {0x3C, 0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x3C}; // Filled circle (disconnected)

// === FIRMWARE & DEVICE INFO ===
const char* FIRMWARE_VERSION = "v0.7";
const int DEVICE_TYPE = 101; // Unique identifier for this *type* of device

// === PIN CONFIGURATION ===
const int SDA_PIN = D2; // I2C Data for Sensor/Display
const int SCL_PIN = D1; // I2C Clock for Sensor/Display
// Note: A0 is used implicitly by analogRead(A0)

// === EEPROM CONFIGURATION ===
// Using EEPROM to store persistent configuration data
#define EEPROM_SIZE 512 // Increased size slightly for safety, adjust if needed

// EEPROM Addresses (ensure no overlaps)
#define EEPROM_ADDR_ADC_CAL          0  // Float (4 bytes)
#define EEPROM_ADDR_TEMP_CAL         4  // Float (4 bytes)
#define EEPROM_ADDR_HUMID_CAL        8  // Float (4 bytes)
#define EEPROM_ADDR_NAME             12 // String (up to FRIENDLY_NAME_MAXLEN bytes)
#define FRIENDLY_NAME_MAXLEN         48 // Max length for friendly name (incl. null)
#define EEPROM_ADDR_PUBLISH_INTERVAL (EEPROM_ADDR_NAME + FRIENDLY_NAME_MAXLEN) // Unsigned Long (4 bytes) ~60
// --- Gap ---
#define EEPROM_ADDR_WIFI_SSID        128 // String (up to WIFI_CRED_MAXLEN bytes)
#define WIFI_CRED_MAXLEN             64  // Max length for SSID/Password (incl. null)
#define EEPROM_ADDR_WIFI_PASS        (EEPROM_ADDR_WIFI_SSID + WIFI_CRED_MAXLEN) // String (up to WIFI_CRED_MAXLEN bytes) ~192

// === WIFI CONFIGURATION ===
#define WIFI_RETRY_LIMIT 30        // How many 500ms attempts before AP mode
#define WIFI_CONNECT_TIMEOUT_MS (WIFI_RETRY_LIMIT * 500)
const char* DEFAULT_WIFI_SSID = "HaciendaHagansLR"; // Default if EEPROM is empty
const char* DEFAULT_WIFI_PASS = "F1nglongers";    // Default if EEPROM is empty
char wifi_ssid[WIFI_CRED_MAXLEN];                 // Buffer for current SSID
char wifi_pass[WIFI_CRED_MAXLEN];                 // Buffer for current Password

// === MQTT CONFIGURATION ===
const char* MQTT_SERVER = "45.32.221.98";
const int MQTT_PORT = 1883;
const char* MQTT_USER = "sensoruser";
const char* MQTT_PASS = "farklebobstein69";
// MQTT Topics will be constructed in setup() based on NodeID and metadata

// === MQTT METADATA (Used for Topic Construction & Payload) ===
// Consider making some of these configurable via MQTT/EEPROM if needed
const char* VENDOR = "mikehagans";
const int VENDOR_ID = 0; // Example ID
const char* ORG = "Hacienda";
const int ORG_ID = 100; // Example ID
const char* AGGREGATOR = "na"; // Example
const char* SITE = "barn";
const char* SITE_ID = "2"; // Example ID
const char* NODE_LOCATION = "DeepFreezer1"; // Physical location/group served by this node

// === SENSOR CONFIGURATION ===
Adafruit_AM2320 am2320 = Adafruit_AM2320();
#define SENSOR_READ_INTERVAL_MS 2000 // How often to read the sensor (AM2320 needs >1.5s between reads)

// === DISPLAY CONFIGURATION ===
#define SCREEN_WIDTH 128 // OLED display width, in pixels
#define SCREEN_HEIGHT 32 // OLED display height, in pixels
#define OLED_RESET_PIN -1 // Reset pin # (or -1 if sharing Arduino reset pin)
#define DISPLAY_UPDATE_INTERVAL_MS 1000 // How often to refresh the display

// === TIMING & INTERVALS ===
#define PUBLISH_INTERVAL_DEFAULT_MS 30000 // Default MQTT publish interval if not set
#define MQTT_RECONNECT_INTERVAL_MS 5000 // Wait 5s between MQTT reconnect attempts

// === OBJECTS ===
WiFiClient espClient;
PubSubClient mqttClient(espClient);
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET_PIN);
ESP8266WebServer webServer(80);
DNSServer dnsServer;

// === GLOBAL VARIABLES ===
bool displayOK = false;           // Was the display initialized successfully?
bool configModeActive = false;    // Is the device in AP Config Portal mode?
float adcCalibration = 1.00;      // Calibration factor for ADC reading
float sensorCalOffsetTemp = 0.0;  // Calibration offset for temperature
float sensorCalOffsetHumidity = 0.0;// Calibration offset for humidity
unsigned long publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS; // Current MQTT publish interval
char friendlyName[FRIENDLY_NAME_MAXLEN] = "rackroom"; // User-friendly name for the device
char nodeID[7];                   // Unique 6-char Base62 Node ID from ChipID + Null terminator

// MQTT Topic Strings (will be populated in setup)
String publishTopic;
String configTopic;
String statusTopic; // Optional: For online/offline status

// State variables for timing and last known values
unsigned long lastPublishTimestamp = 0;
unsigned long lastDisplayUpdateTimestamp = 0;
unsigned long lastSensorUpdateTimestamp = 0;
unsigned long lastMqttAttemptTimestamp = 0;
float lastTempC = NAN;            // Use NAN to indicate no valid reading yet
float lastHumidity = NAN;         // Use NAN to indicate no valid reading yet
int lastADCPercent = -1;          // Use -1 to indicate no valid reading yet

// === FUNCTION PROTOTYPES ===
// --- Configuration (EEPROM) ---
void loadGeneralConfig();
void saveGeneralConfig();
void loadWiFiCredentials();
void saveWiFiCredentials(const String& newSSID, const String& newPASS);
// --- WiFi & Network ---
void setupWiFi();
void startConfigPortal();
void handleWebServer();
void handleDns();
// --- MQTT ---
void setupMQTT();
void connectMQTT();
void mqttCallback(char* topic, byte* payload, unsigned int length);
void applyConfigPayload(const char* payload);
void publishSensorData();
void publishStatus(const char* status); // Optional: Publish online/offline
// --- Sensor ---
void setupSensor();
void updateSensorReadings();
// --- Display ---
void setupDisplay();
void updateDisplay();
void drawTopStatusBar();
void drawPublishProgressBar();
const uint8_t* getWifiIcon(int level, uint8_t &width);
int wifiLevelFromRSSI(long rssi);
// --- Utilities ---
void generateNodeID();
void base62Encode(uint32_t value, char *output, int length);

// === UTILITIES ===
const char base62Chars[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

void base62Encode(uint32_t value, char *output, int length) {
  for (int i = length - 1; i >= 0; --i) {
    output[i] = base62Chars[value % 62];
    value /= 62;
  }
  output[length] = '\0'; // Null-terminate the string
}

void generateNodeID() {
  uint32_t chipId = ESP.getChipId();
  base62Encode(chipId, nodeID, 6); // Generate 6-character base62 ID
  Serial.print("Generated NodeID: ");
  Serial.println(nodeID);
}
