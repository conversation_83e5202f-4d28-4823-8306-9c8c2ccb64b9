// NodeMCU12_MQTT_AM2320_OLED_v0.7 (Reviewed & Optimized)
// - Improved organization and comments
// - Added minor robustness checks (MQTT publish)
// - Consistent use of const for pins
// - Minor code style adjustments

// === LIBRARIES ===
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <DNSServer.h>
#include <PubSubClient.h>
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_AM2320.h>       // AM2320 Temp/Humidity Sensor
#include <Adafruit_SSD1306.h>     // OLED Display Driver
#include <Adafruit_GFX.h>         // Graphics library for display
#include <ArduinoJson.h>          // MQTT Payload Handling
#include <EEPROM.h>               // Configuration Storage
#include <icons.h>

// === FIRMWARE & DEVICE INFO ===
const char* FIRMWARE_VERSION = "v0.7";
const int DEVICE_TYPE = 101; // Unique identifier for this *type* of device

// === PIN CONFIGURATION ===
const int SDA_PIN = D2; // I2C Data for Sensor/Display
const int SCL_PIN = D1; // I2C Clock for Sensor/Display
// Note: A0 is used implicitly by analogRead(A0)

// === EEPROM CONFIGURATION ===
// Using EEPROM to store persistent configuration data
#define EEPROM_SIZE 512 // Increased size slightly for safety, adjust if needed

// EEPROM Addresses (ensure no overlaps)
#define EEPROM_ADDR_ADC_CAL          0  // Float (4 bytes)
#define EEPROM_ADDR_TEMP_CAL         4  // Float (4 bytes)
#define EEPROM_ADDR_HUMID_CAL        8  // Float (4 bytes)
#define EEPROM_ADDR_NAME             12 // String (up to FRIENDLY_NAME_MAXLEN bytes)
#define FRIENDLY_NAME_MAXLEN         48 // Max length for friendly name (incl. null)
#define EEPROM_ADDR_PUBLISH_INTERVAL (EEPROM_ADDR_NAME + FRIENDLY_NAME_MAXLEN) // Unsigned Long (4 bytes) ~60
// --- Gap ---
#define EEPROM_ADDR_WIFI_SSID        128 // String (up to WIFI_CRED_MAXLEN bytes)
#define WIFI_CRED_MAXLEN             64  // Max length for SSID/Password (incl. null)
#define EEPROM_ADDR_WIFI_PASS        (EEPROM_ADDR_WIFI_SSID + WIFI_CRED_MAXLEN) // String (up to WIFI_CRED_MAXLEN bytes) ~192

// === WIFI CONFIGURATION ===
#define WIFI_RETRY_LIMIT 30        // How many 500ms attempts before AP mode
#define WIFI_CONNECT_TIMEOUT_MS (WIFI_RETRY_LIMIT * 500)
const char* DEFAULT_WIFI_SSID = "HaciendaHagansLR"; // Default if EEPROM is empty
const char* DEFAULT_WIFI_PASS = "F1nglongers";    // Default if EEPROM is empty
char wifi_ssid[WIFI_CRED_MAXLEN];                 // Buffer for current SSID
char wifi_pass[WIFI_CRED_MAXLEN];                 // Buffer for current Password

// === MQTT CONFIGURATION ===
const char* MQTT_SERVER = "45.32.221.98";
const int MQTT_PORT = 1883;
const char* MQTT_USER = "sensoruser";
const char* MQTT_PASS = "farklebobstein69";
// MQTT Topics will be constructed in setup() based on NodeID and metadata

// === MQTT METADATA (Used for Topic Construction & Payload) ===
// Consider making some of these configurable via MQTT/EEPROM if needed
const char* VENDOR = "mikehagans";
const int VENDOR_ID = 0; // Example ID
const char* ORG = "stingray";
const int ORG_ID = 100; // Example ID
const char* AGGREGATOR = "na"; // Example
const char* SITE = "logic_bunker";
const char* SITE_ID = "2"; // Example ID
const char* NODE_LOCATION = "Tx_Rack"; // Physical location/group served by this node

// === SENSOR CONFIGURATION ===
Adafruit_AM2320 am2320 = Adafruit_AM2320();
#define SENSOR_READ_INTERVAL_MS 2000 // How often to read the sensor (AM2320 needs >1.5s between reads)

// === DISPLAY CONFIGURATION ===
#define SCREEN_WIDTH 128 // OLED display width, in pixels
#define SCREEN_HEIGHT 32 // OLED display height, in pixels
#define OLED_RESET_PIN -1 // Reset pin # (or -1 if sharing Arduino reset pin)
#define DISPLAY_UPDATE_INTERVAL_MS 1000 // How often to refresh the display

// === TIMING & INTERVALS ===
#define PUBLISH_INTERVAL_DEFAULT_MS 30000 // Default MQTT publish interval if not set
#define MQTT_RECONNECT_INTERVAL_MS 5000 // Wait 5s between MQTT reconnect attempts

// === OBJECTS ===
WiFiClient espClient;
PubSubClient mqttClient(espClient);
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET_PIN);
ESP8266WebServer webServer(80);
DNSServer dnsServer;

// === GLOBAL VARIABLES ===
bool displayOK = false;           // Was the display initialized successfully?
bool configModeActive = false;    // Is the device in AP Config Portal mode?
float adcCalibration = 1.00;      // Calibration factor for ADC reading
float sensorCalOffsetTemp = 0.0;  // Calibration offset for temperature
float sensorCalOffsetHumidity = 0.0;// Calibration offset for humidity
unsigned long publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS; // Current MQTT publish interval
char friendlyName[FRIENDLY_NAME_MAXLEN] = "rackroom"; // User-friendly name for the device
char nodeID[7];                   // Unique 6-char Base62 Node ID from ChipID + Null terminator

// MQTT Topic Strings (will be populated in setup)
String publishTopic;
String configTopic;
String statusTopic; // Optional: For online/offline status

// State variables for timing and last known values
unsigned long lastPublishTimestamp = 0;
unsigned long lastDisplayUpdateTimestamp = 0;
unsigned long lastSensorUpdateTimestamp = 0;
unsigned long lastMqttAttemptTimestamp = 0;
float lastTempC = NAN;            // Use NAN to indicate no valid reading yet
float lastHumidity = NAN;         // Use NAN to indicate no valid reading yet
int lastADCPercent = -1;          // Use -1 to indicate no valid reading yet

// === FUNCTION PROTOTYPES ===
// --- Configuration (EEPROM) ---
void loadGeneralConfig();
void saveGeneralConfig();
void loadWiFiCredentials();
void saveWiFiCredentials(const String& newSSID, const String& newPASS);
// --- WiFi & Network ---
void setupWiFi();
void startConfigPortal();
void handleWebServer();
void handleDns();
// --- MQTT ---
void setupMQTT();
void connectMQTT();
void mqttCallback(char* topic, byte* payload, unsigned int length);
void applyConfigPayload(const char* payload);
void publishSensorData();
void publishStatus(const char* status); // Optional: Publish online/offline
// --- Sensor ---
void setupSensor();
void updateSensorReadings();
// --- Display ---
void setupDisplay();
void updateDisplay();
void drawTopStatusBar();
void drawPublishProgressBar();
const uint8_t* getWifiIcon(int level, uint8_t &width);
int wifiLevelFromRSSI(long rssi);
// --- Utilities ---
void generateNodeID();
void base62Encode(uint32_t value, char *output, int length);

// === UTILITIES ===
const char base62Chars[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

void base62Encode(uint32_t value, char *output, int length) {
  for (int i = length - 1; i >= 0; --i) {
    output[i] = base62Chars[value % 62];
    value /= 62;
  }
  output[length] = '\0'; // Null-terminate the string
}

void generateNodeID() {
  uint32_t chipId = ESP.getChipId();
  base62Encode(chipId, nodeID, 6); // Generate 6-character base62 ID
  Serial.print("Generated NodeID: ");
  Serial.println(nodeID);
}

// === EEPROM FUNCTIONS ===
void loadWiFiCredentials() {
  Serial.println("Loading WiFi credentials from EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  bool credsValid = true;
  // Read SSID
  for (int i = 0; i < WIFI_CRED_MAXLEN; i++) {
    wifi_ssid[i] = EEPROM.read(EEPROM_ADDR_WIFI_SSID + i);
    if (i == 0 && wifi_ssid[i] == 0xFF) { // Check if EEPROM seems uninitialized
        credsValid = false;
        break;
    }
    if (wifi_ssid[i] == '\0') break; // Stop at null terminator
  }
  wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination

  // Read Password only if SSID seemed valid
  if (credsValid) {
      for (int i = 0; i < WIFI_CRED_MAXLEN; i++) {
        wifi_pass[i] = EEPROM.read(EEPROM_ADDR_WIFI_PASS + i);
        if (wifi_pass[i] == '\0') break; // Stop at null terminator
      }
      wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
  }

  EEPROM.end();

  // If EEPROM was empty/invalid or strings are zero length, use defaults
  if (!credsValid || strlen(wifi_ssid) == 0 ) { // Don't require password to be present
    Serial.println("EEPROM WiFi credentials invalid or empty, using defaults.");
    strncpy(wifi_ssid, DEFAULT_WIFI_SSID, WIFI_CRED_MAXLEN -1);
    strncpy(wifi_pass, DEFAULT_WIFI_PASS, WIFI_CRED_MAXLEN -1);
    wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
    wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0';
  } else {
      Serial.println("WiFi credentials loaded successfully.");
  }

  Serial.print("SSID: "); Serial.println(wifi_ssid);
  // Avoid printing password to Serial for security, uncomment if needed for debug
  // Serial.print("Password: "); Serial.println(wifi_pass);
}

void saveWiFiCredentials(const String& newSSID, const String& newPASS) {
  Serial.println("Saving WiFi credentials to EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  // Clear old SSID area first (optional, but good practice)
  for (int i = 0; i < WIFI_CRED_MAXLEN; i++) EEPROM.write(EEPROM_ADDR_WIFI_SSID + i, 0);
  // Write new SSID
  for (unsigned int i = 0; i < newSSID.length() && i < (unsigned int)(WIFI_CRED_MAXLEN -1); i++) {
    EEPROM.write(EEPROM_ADDR_WIFI_SSID + i, newSSID[i]);
  }
  // Clear old Password area
  for (int i = 0; i < WIFI_CRED_MAXLEN; i++) EEPROM.write(EEPROM_ADDR_WIFI_PASS + i, 0);
  // Write new Password
  for (unsigned int i = 0; i < newPASS.length() && i < (unsigned int)(WIFI_CRED_MAXLEN - 1); i++) {
    EEPROM.write(EEPROM_ADDR_WIFI_PASS + i, newPASS[i]);
  }

  if (EEPROM.commit()) {
    Serial.println("EEPROM WiFi credentials saved successfully.");
  } else {
    Serial.println("ERROR: EEPROM commit failed!");
  }
  EEPROM.end();

  // Update in-memory buffers immediately
  strncpy(wifi_ssid, newSSID.c_str(), WIFI_CRED_MAXLEN -1);
  strncpy(wifi_pass, newPASS.c_str(), WIFI_CRED_MAXLEN -1);
   wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
   wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0';
}

void loadGeneralConfig() {
  Serial.println("Loading general config from EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  EEPROM.get(EEPROM_ADDR_ADC_CAL, adcCalibration);
  EEPROM.get(EEPROM_ADDR_TEMP_CAL, sensorCalOffsetTemp);
  EEPROM.get(EEPROM_ADDR_HUMID_CAL, sensorCalOffsetHumidity);
  EEPROM.get(EEPROM_ADDR_PUBLISH_INTERVAL, publishIntervalMs);

  // Validate loaded values
  if (isnan(adcCalibration)) adcCalibration = 1.0;
  if (isnan(sensorCalOffsetTemp)) sensorCalOffsetTemp = 0.0;
  if (isnan(sensorCalOffsetHumidity)) sensorCalOffsetHumidity = 0.0;
  if (publishIntervalMs < 5000 || publishIntervalMs > 3600000) { // 5sec to 1hr
    Serial.print("Invalid publish interval from EEPROM, using default: ");
    Serial.println(PUBLISH_INTERVAL_DEFAULT_MS);
    publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS;
  }

  // Read friendly name string
  bool nameValid = false;
  for (int i = 0; i < FRIENDLY_NAME_MAXLEN; i++) {
    friendlyName[i] = EEPROM.read(EEPROM_ADDR_NAME + i);
    if (i==0 && friendlyName[i] != 0xFF) nameValid = true; // Basic check if initialized
    if (friendlyName[i] == '\0') break;
  }
  friendlyName[FRIENDLY_NAME_MAXLEN - 1] = '\0'; // Ensure null termination
  if (!nameValid || strlen(friendlyName) == 0) {
      Serial.println("Using default friendly name.");
      strcpy(friendlyName, "Default Sensor"); // Set a default if empty
  }

  EEPROM.end();
  Serial.println("General config loaded.");
  Serial.print(" ADC Cal: "); Serial.println(adcCalibration);
  Serial.print(" Temp Offset: "); Serial.println(sensorCalOffsetTemp);
  Serial.print(" Humid Offset: "); Serial.println(sensorCalOffsetHumidity);
  Serial.print(" Publish Interval: "); Serial.println(publishIntervalMs);
  Serial.print(" Friendly Name: "); Serial.println(friendlyName);
}

void saveGeneralConfig() {
  Serial.println("Saving general config to EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  EEPROM.put(EEPROM_ADDR_ADC_CAL, adcCalibration);
  EEPROM.put(EEPROM_ADDR_TEMP_CAL, sensorCalOffsetTemp);
  EEPROM.put(EEPROM_ADDR_HUMID_CAL, sensorCalOffsetHumidity);
  EEPROM.put(EEPROM_ADDR_PUBLISH_INTERVAL, publishIntervalMs);

  // Write friendly name string
  for (int i = 0; i < FRIENDLY_NAME_MAXLEN - 1; i++) {
    EEPROM.write(EEPROM_ADDR_NAME + i, friendlyName[i]);
    if (friendlyName[i] == '\0') break; // Stop writing after null terminator
  }
  EEPROM.write(EEPROM_ADDR_NAME + FRIENDLY_NAME_MAXLEN - 1, '\0'); // Ensure last byte is null term

  if (EEPROM.commit()) {
    Serial.println("EEPROM general config saved successfully.");
  } else {
    Serial.println("ERROR: EEPROM commit failed!");
  }
  EEPROM.end();
}


// === CONFIG PORTAL ===
void startConfigPortal() {
  configModeActive = true;
  Serial.println("Starting WiFi Configuration Portal...");

  if (displayOK) {
    display.clearDisplay();
    display.setTextSize(1);
    display.setCursor(0, 0);
    display.println("Starting AP Mode...");
    display.display();
  }

  delay(500);

  WiFi.mode(WIFI_AP);
  String apName = "SensorSetup_" + String(nodeID); // Create unique AP name
  Serial.print("AP Name: "); Serial.println(apName);

  // Start AP
  if(WiFi.softAP(apName.c_str())) {
     Serial.print("AP IP address: "); Serial.println(WiFi.softAPIP());

    // Start DNS Server for captive portal
    if(dnsServer.start(53, "*", WiFi.softAPIP())) {
        Serial.println("DNS Server Started.");
    } else {
        Serial.println("Failed to start DNS Server!");
    }

    // --- Web Server Handlers ---
    webServer.on("/", HTTP_GET, []() {
      String html = R"(
        <html><head><title>Sensor WiFi Setup</title>
        <style>body{font-family: sans-serif; margin: 20px;}
               label{display: block; margin-bottom: 5px;}
               input{width: 90%; padding: 8px; margin-bottom: 10px; border: 1px solid #ccc;}
               input[type=submit]{background: #007cba; color: white; border: none; padding: 10px 20px; cursor: pointer;}
        </style>
        </head><body>
        <h2>Configure WiFi Credentials</h2>
        <p>Enter your WiFi network credentials:</p>
        <form action='/save' method='POST'>
          <label for='ssid'>SSID (Network Name):</label>
          <input id='ssid' name='ssid' type='text' required><br>
          <label for='pass'>Password:</label>
          <input id='pass' name='pass' type='password'><br><br>
          <input type='submit' value='Save & Reboot'>
        </form></body></html>)";
      webServer.send(200, "text/html", html);
    });

    webServer.on("/save", HTTP_POST, []() {
      String newSSID = webServer.arg("ssid");
      String newPASS = webServer.arg("pass");
      Serial.println("Received new WiFi credentials via web form.");
      Serial.print(" New SSID: "); Serial.println(newSSID);
      // Avoid printing password: Serial.print(" New Pass: "); Serial.println(newPASS);

      saveWiFiCredentials(newSSID, newPASS); // Save to EEPROM

      String html = R"(
        <html><head><title>Saved</title></head><body>
        <h2>WiFi Credentials Saved!</h2><p>Device will now reboot and attempt to connect.</p>
        </body></html>)";
      webServer.send(200, "text/html", html);

      delay(2000); // Give browser time to receive response
      ESP.restart(); // Reboot to apply new settings
    });

    // --- Captive Portal Handlers ---
    // Android redirects
    webServer.on("/generate_204", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    // iOS redirects
    webServer.on("/hotspot-detect.html", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    webServer.on("/captive.apple.com", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    // Microsoft redirects
    webServer.on("/fwlink", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    // Generic redirect for anything else
    webServer.onNotFound([]() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });

    webServer.begin();
    Serial.println("Web Server started.");

    if (displayOK) {
        display.clearDisplay();
        display.setTextSize(1);
        display.setCursor(0, 0);
        display.println("CONFIG MODE ACTIVE");
        display.setCursor(0, 10);
        display.print("Connect to AP:");
        display.setCursor(0, 20);
        display.println(apName);
        display.display();
    }

  } else {
      Serial.println("Failed to start Soft AP!");
      if (displayOK) {
          display.clearDisplay();
          display.setTextSize(1);
          display.setCursor(0, 10);
          display.println("AP Start Failed!");
          display.display();
      }
      // Maybe retry or signal error state
  }
}

void handleWebServer() {
  if(configModeActive) {
    webServer.handleClient();
  }
}

void handleDns() {
    if(configModeActive) {
        dnsServer.processNextRequest();
    }
}

// === WIFI ===
void setupWiFi() {
  generateNodeID(); // Generate the unique ID first
  loadWiFiCredentials(); // Load credentials from EEPROM or defaults

  WiFi.mode(WIFI_STA); // Set to Station mode
  WiFi.hostname(nodeID); // Set hostname to NodeID (optional, good for network scanning)

  Serial.print("Attempting to connect to WiFi SSID: ");
  Serial.println(wifi_ssid);

  if (displayOK) {
    display.clearDisplay();
    display.setTextSize(1);
    display.setCursor(0, 0);
    display.println("Connecting WiFi:");
    display.setCursor(0, 10);
    display.println(wifi_ssid);
    // Optionally add connecting animation here
    display.display();
  }

  delay(100); // Short delay before starting connection attempt
  WiFi.begin(wifi_ssid, wifi_pass);
  WiFi.setAutoReconnect(true); // Automatically reconnect if connection drops
  WiFi.persistent(true);    // Store credentials in SDK flash (redundant if using EEPROM but doesn't hurt)

  Serial.print("Connecting...");
  unsigned long startAttemptTime = millis();
  while (WiFi.status() != WL_CONNECTED && (millis() - startAttemptTime < WIFI_CONNECT_TIMEOUT_MS)) {
    Serial.print(".");
    delay(500);
    // Could update a connecting animation on the display here
  }
  Serial.println();

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi Connected!");
    Serial.print("IP Address: "); Serial.println(WiFi.localIP());
    Serial.print("RSSI: "); Serial.println(WiFi.RSSI());
    if (displayOK) {
      display.clearDisplay();
      display.setTextSize(1);
      display.setCursor(0, 0);
      display.println("WiFi Connected!");
      display.setCursor(0, 10);
      display.println(WiFi.localIP());
      display.setCursor(0, 20);
      display.print("RSSI: "); display.print(WiFi.RSSI()); display.println(" dBm");
      display.display();
      delay(2000); // Show connection success message briefly
    }
  } else {
    Serial.println("WiFi Connection Failed after timeout.");
    if (displayOK) {
      display.clearDisplay();
      display.setTextSize(1);
      display.setCursor(0, 0);
      display.println("WiFi Failed!");
      display.setCursor(0, 10);
      display.println("Starting AP Mode...");
      display.display();
      delay(2000);
    }
    startConfigPortal(); // Fallback to AP mode for configuration
  }
}

// === MQTT ===
void setupMQTT() {
  // Construct MQTT topics using metadata and the unique NodeID
  // Format: type/vendor/org/site/location/nodeID/measurement
  // Example: sensors/mikehagans/stingray/logic_bunker/Tx_Rack/aB3dE5/data
  // Example: config/mikehagans/stingray/logic_bunker/Tx_Rack/aB3dE5/set
  // Example: status/mikehagans/stingray/logic_bunker/Tx_Rack/aB3dE5/state
  String baseTopic = String(VENDOR) + "/" + String(ORG) + "/" + String(SITE) + "/" + String(NODE_LOCATION) + "/" + String(nodeID);
  publishTopic = "sensors/" + baseTopic + "/data";
  configTopic  = "config/" + baseTopic + "/set";
  statusTopic  = "status/" + baseTopic + "/state"; // LWT could also use this

  Serial.print("Publish Topic: "); Serial.println(publishTopic);
  Serial.print("Config Topic: "); Serial.println(configTopic);
  Serial.print("Status Topic: "); Serial.println(statusTopic);

  mqttClient.setServer(MQTT_SERVER, MQTT_PORT);
  mqttClient.setCallback(mqttCallback);
  // Consider increasing buffer size if JSON payloads are large:
  // mqttClient.setBufferSize(1024);
}

void connectMQTT() {
  if (!mqttClient.connected()) {
    unsigned long now = millis();
    // Only attempt to reconnect every N seconds
    if (now - lastMqttAttemptTimestamp > MQTT_RECONNECT_INTERVAL_MS) {
      lastMqttAttemptTimestamp = now;
      Serial.print("Attempting MQTT connection...");
      // Create a unique client ID using the NodeID
      String clientID = "esp8266-" + String(nodeID);
      Serial.print(" ClientID: "); Serial.println(clientID);

      // Attempt to connect with username/password and Last Will & Testament (LWT)
      // LWT publishes "offline" to the status topic if the client disconnects unexpectedly
      if (mqttClient.connect(clientID.c_str(), MQTT_USER, MQTT_PASS, statusTopic.c_str(), 0, true, "offline")) {
        Serial.println("MQTT Connected!");
        // Publish "online" status upon successful connection
        publishStatus("online");
        // Subscribe to the configuration topic
        if (mqttClient.subscribe(configTopic.c_str())) {
          Serial.print("Subscribed to config topic: "); Serial.println(configTopic);
        } else {
          Serial.println("ERROR: Failed to subscribe to config topic!");
        }
      } else {
        Serial.print("MQTT connection failed, rc="); Serial.print(mqttClient.state());
        // Print error explanation: https://pubsubclient.knolleary.net/api.html#state
        switch (mqttClient.state()) {
            case MQTT_CONNECTION_TIMEOUT: Serial.println(" (Connection Timeout)"); break;
            case MQTT_CONNECTION_LOST: Serial.println(" (Connection Lost)"); break;
            case MQTT_CONNECT_FAILED: Serial.println(" (Connect Failed)"); break;
            case MQTT_DISCONNECTED: Serial.println(" (Disconnected)"); break;
            case MQTT_CONNECT_BAD_PROTOCOL: Serial.println(" (Bad Protocol)"); break;
            case MQTT_CONNECT_BAD_CLIENT_ID: Serial.println(" (Bad Client ID)"); break;
            case MQTT_CONNECT_UNAVAILABLE: Serial.println(" (Unavailable)"); break;
            case MQTT_CONNECT_BAD_CREDENTIALS: Serial.println(" (Bad Credentials)"); break;
            case MQTT_CONNECT_UNAUTHORIZED: Serial.println(" (Unauthorized)"); break;
            default: Serial.println(" (Unknown Error)"); break;
        }
        Serial.println(" Will retry...");
      }
    }
  }
}

// Callback function for processing incoming MQTT messages
void mqttCallback(char* topic, byte* payload, unsigned int length) {
  Serial.print("Message arrived ["); Serial.print(topic); Serial.print("] ");
  char msg[length + 1]; // Create buffer for payload
  memcpy(msg, payload, length);
  msg[length] = '\0'; // Null-terminate the payload string
  Serial.println(msg);

  // Check if the message is on our configuration topic
  if (String(topic) == configTopic) {
      applyConfigPayload(msg);
  } else {
      Serial.println(" (Ignoring message on unknown topic)");
  }
}

// Process JSON payload received on the config topic
void applyConfigPayload(const char* payload) {
  Serial.println("Applying configuration payload...");
  StaticJsonDocument <256> doc; // Adjust size if needed
  DeserializationError error = deserializeJson(doc, payload);

  if (error) {
    Serial.print("deserializeJson() failed: ");
    Serial.println(error.c_str());
    return; // Exit if JSON parsing fails
  }

  bool configChanged = false; // Flag to track if EEPROM needs saving

  // Safely check for and apply each configuration key
  if (doc.containsKey("adc_calibration")) {
    float val = doc["adc_calibration"];
    if (adcCalibration != val) {
      Serial.print("Updating adc_calibration: "); Serial.println(val);
      adcCalibration = val;
      configChanged = true;
    }
  }
  if (doc.containsKey("temp_offset")) {
    float val = doc["temp_offset"];
    if (sensorCalOffsetTemp != val) {
      Serial.print("Updating temp_offset: "); Serial.println(val);
      sensorCalOffsetTemp = val;
      configChanged = true;
    }
  }
  if (doc.containsKey("humidity_offset")) {
    float val = doc["humidity_offset"];
    if (sensorCalOffsetHumidity != val) {
       Serial.print("Updating humidity_offset: "); Serial.println(val);
      sensorCalOffsetHumidity = val;
      configChanged = true;
    }
  }
  if (doc.containsKey("publish_interval_ms")) {
    unsigned long val = doc["publish_interval_ms"];
    // Add validation for the received interval
    if (val >= 5000 && val <= 3600000) { // 5sec to 1hr limit
        if (val != publishIntervalMs) {
            Serial.print("Updating publish_interval_ms: "); Serial.println(val);
            publishIntervalMs = val;
            configChanged = true;
        }
    } else {
        Serial.print("Ignoring invalid publish_interval_ms: "); Serial.println(val);
    }
  }
  if (doc.containsKey("friendlyName")) {
    const char* val = doc["friendlyName"];
    if (val != nullptr && strncmp(friendlyName, val, FRIENDLY_NAME_MAXLEN) != 0) {
      Serial.print("Updating friendlyName: "); Serial.println(val);
      strncpy(friendlyName, val, FRIENDLY_NAME_MAXLEN - 1);
      friendlyName[FRIENDLY_NAME_MAXLEN - 1] = '\0'; // Ensure null termination
      configChanged = true;
    }
  }

  // If any configuration was changed, save it to EEPROM
  if (configChanged) {
    saveGeneralConfig();
    // Optional: Republish current state or acknowledge config change via MQTT
  } else {
      Serial.println("No configuration changes detected in payload.");
  }
}

// Publish sensor data to the MQTT broker
void publishSensorData() {
  // Only publish if we have valid sensor readings
  if (isnan(lastTempC) || isnan(lastHumidity)) {
      Serial.println("Skipping MQTT publish: Invalid sensor data.");
      return;
  }
  if (!mqttClient.connected()) {
      Serial.println("Skipping MQTT publish: Client not connected.");
      return;
  }

  StaticJsonDocument <512> doc; // Adjust size if needed

  // --- Metadata ---
  doc["device_type"] = DEVICE_TYPE;
  doc["node_id"] = nodeID;
  doc["fw_version"] = FIRMWARE_VERSION;
  doc["friendly_name"] = friendlyName;
  doc["vendor"] = VENDOR;
  doc["org"] = ORG;
  doc["site"] = SITE;
  doc["location"] = NODE_LOCATION; // Renamed 'node' to 'location' for clarity
  doc["timestamp"] = millis(); // Add a simple timestamp (consider NTP for real time)
  doc["rssi"] = WiFi.RSSI(); // Include WiFi signal strength

  // --- Sensor Values ---
  JsonObject values = doc.createNestedObject("values");
  // Apply calibration offsets before publishing
  values["temperature_c"] = lastTempC + sensorCalOffsetTemp;
  values["humidity_percent"] = lastHumidity + sensorCalOffsetHumidity;
  values["adc_percent"] = lastADCPercent; // Assuming ADC calibration already applied in updateSensorReadings

  // Serialize JSON payload to a buffer
  char payloadBuffer[512];
  size_t n = serializeJson(doc, payloadBuffer);

  Serial.print("Publishing to MQTT topic ["); Serial.print(publishTopic); Serial.print("]: ");
  Serial.println(payloadBuffer);

  // Publish the message
  if (mqttClient.publish(publishTopic.c_str(), payloadBuffer, n)) {
    Serial.println("MQTT Publish successful.");
    lastPublishTimestamp = millis(); // Update timestamp only on successful publish
  } else {
    Serial.println("ERROR: MQTT Publish failed!");
    // Handle failed publish (e.g., buffer full, connection issue) - maybe retry later?
  }
}

// Optional: Publish device status (online/offline)
void publishStatus(const char* status) {
    if (mqttClient.connected()) {
        mqttClient.publish(statusTopic.c_str(), status, true); // Publish with retain flag
        Serial.print("Published status '"); Serial.print(status); Serial.print("' to "); Serial.println(statusTopic);
    }
}


// === SENSOR ===
void setupSensor() {
  Serial.println("Initializing AM2320 sensor...");
  am2320.begin();
  // No easy return value check, we'll rely on isnan() checks later
  delay(100); // Small delay after init
}

void updateSensorReadings() {
  unsigned long now = millis();
  // Read sensor only at the specified interval
  if (now - lastSensorUpdateTimestamp >= SENSOR_READ_INTERVAL_MS) {
    lastSensorUpdateTimestamp = now;

    Serial.println("Reading sensors...");

    // Read Temperature and Humidity from AM2320
    float tempC = am2320.readTemperature(); // Reads temperature in Celsius
    float humidity = am2320.readHumidity();

    // Read Analog Input (A0) and apply calibration
    int adcRaw = analogRead(A0);
    // Apply calibration factor, ensuring result stays within 0-100%
    lastADCPercent = constrain((int)(adcRaw / 1023.0 * 100.0 * adcCalibration), 0, 100);

    // Validate AM2320 readings (check for NAN - Not A Number)
    if (isnan(tempC) || isnan(humidity)) {
      Serial.println("Failed to read from AM2320 sensor!");
      // Keep previous valid readings or explicitly set to NAN? Decide based on requirements.
      // Setting to NAN ensures bad data isn't displayed or published.
      // lastTempC = NAN; // Uncomment if you want failed reads to clear old values
      // lastHumidity = NAN;
    } else {
      // Readings seem valid, update the global variables
      lastTempC = tempC;
      lastHumidity = humidity;
      Serial.print("  Temp: "); Serial.print(lastTempC); Serial.print(" *C");
      Serial.print("  Humidity: "); Serial.print(lastHumidity); Serial.print(" %");
      Serial.print("  ADC Raw: "); Serial.print(adcRaw);
      Serial.print("  ADC Percent (Cal): "); Serial.println(lastADCPercent);
    }
  }
}



// Helper to get the correct WiFi icon based on signal level
const uint8_t* getWifiIcon(int level, uint8_t &width) {
  width = 8; // All icons are 8 pixels wide
  switch (level) {
    case 1: return wifi_icon_1;
    case 2: return wifi_icon_2;
    case 3: return wifi_icon_3;
    case 4: return wifi_icon_4;
    case 5: return wifi_icon_5;
    case 6: return wifi_icon_6;
    default: return wifi_icon_0; // Level 0 or unknown
  }
}

// Convert RSSI value to a signal level (0-6)
int wifiLevelFromRSSI(long rssi) {
  if (WiFi.status() != WL_CONNECTED) return 0; // No connection = level 0
  if (rssi >= -55) return 6; // Excellent
  else if (rssi >= -65) return 5; // Good
  else if (rssi >= -70) return 4; // Fair
  else if (rssi >= -75) return 3; // Okay
  else if (rssi >= -85) return 2; // Weak
  else return 1;                  // Very Weak
}

void setupDisplay() {
  Serial.println("Initializing OLED display...");
  // Address 0x3C for 128x32
  if (display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
    displayOK = true;
    Serial.println("OLED display initialized successfully.");
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("Initializing...");
    display.display();
    delay(500); // Show init message briefly
  } else {
    displayOK = false;
    Serial.println("ERROR: OLED display initialization failed!");
  }
}

// Draw the top status bar (WiFi, NodeID, MQTT Status)
void drawTopStatusBar() {
  if (!displayOK) return;

  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE); // Ensure text is white
  display.fillRect(0, 0, SCREEN_WIDTH, 9, SSD1306_BLACK); // Clear status bar area

  // --- WiFi Status ---
  uint8_t iconWidth = 8;
  long currentRSSI = (WiFi.status() == WL_CONNECTED) ? WiFi.RSSI() : -100; // Get RSSI or default
  int wifiLevel = wifiLevelFromRSSI(currentRSSI);
  const uint8_t* wifiIcon = getWifiIcon(wifiLevel, iconWidth);
  display.drawBitmap(0, 0, wifiIcon, iconWidth, 8, SSD1306_WHITE);
  // Optionally display RSSI value next to icon if space allows

  // --- Node ID ---
  display.setCursor((SCREEN_WIDTH / 2) - (strlen(nodeID) * 6 / 2), 0); // Center NodeID roughly
  display.print(nodeID);

  // --- MQTT Status ---
  const uint8_t* mqttStatusIcon = mqttClient.connected() ? checkmark_icon : x_icon;
  display.drawBitmap(SCREEN_WIDTH - 18, 0, mqtt_icon, 8, 8, SSD1306_WHITE); // Cloud icon
  display.drawBitmap(SCREEN_WIDTH - 8, 0, mqttStatusIcon, 8, 8, SSD1306_WHITE); // Check or X
}

// Draw the progress bar at the bottom showing time until next publish
void drawPublishProgressBar() {
  if (!displayOK) return;
  display.fillRect(0, SCREEN_HEIGHT - 2, SCREEN_WIDTH, 2, SSD1306_BLACK); // Clear bar area

  unsigned long now = millis();
  // Calculate elapsed time since last *successful* publish
  unsigned long elapsed = (lastPublishTimestamp == 0) ? 0 : (now - lastPublishTimestamp);
  if (elapsed > publishIntervalMs) {
      elapsed = publishIntervalMs; // Cap at max interval
  }

  // Map elapsed time to screen width for the progress bar
  int barWidth = map(elapsed, 0, publishIntervalMs, 0, SCREEN_WIDTH);
  display.fillRect(0, SCREEN_HEIGHT - 1, barWidth, 1, SSD1306_WHITE); // Draw bar
}

// Main function to update the entire display content
void updateDisplay() {
  if (!displayOK || configModeActive) return; // Don't update display in config mode

  unsigned long now = millis();
  if (now - lastDisplayUpdateTimestamp >= DISPLAY_UPDATE_INTERVAL_MS) {
    lastDisplayUpdateTimestamp = now;

    display.clearDisplay(); // Clear previous content

    drawTopStatusBar(); // Draw WiFi, NodeID, MQTT status

    // --- Sensor Data Area ---
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 10); // Position below status bar

    if (isnan(lastTempC) || isnan(lastHumidity)) {
      // Display Sensor Error Message
      display.drawBitmap(0, 12, sensor_icon, 8, 8, SSD1306_WHITE);
      display.setTextSize(2);
      display.setCursor(12, 12); // Position next to icon
      display.println(F("Sensor")); // Use F() macro for static strings
      display.setCursor(12, 24);
      display.println(F("Error!"));
    } else {
      // Display Valid Sensor Readings
      // Temperature
      display.drawBitmap(0, 11, sensor_icon, 8, 8, SSD1306_WHITE); // Thermometer icon
      display.setCursor(10, 10);
      display.printf("T:%.1f", lastTempC + sensorCalOffsetTemp); // Apply offset for display
      int tempX = display.getCursorX();
      int tempY = display.getCursorY();
      display.drawCircle(tempX + 2, tempY + 0, 1, SSD1306_WHITE); // Degree symbol °
      display.print("C");

      // Humidity
      display.setCursor(SCREEN_WIDTH / 2 + 5, 10); // Position humidity on the right
      display.printf("H:%.1f%%", lastHumidity + sensorCalOffsetHumidity); // Apply offset

      // ADC Value
      display.setCursor(10, 22); // Below Temperature
      display.printf("ADC: %d%%", lastADCPercent);
    }

    drawPublishProgressBar(); // Draw the progress bar at the bottom

    display.display(); // Send buffer to the OLED
  }
}

// === SETUP ===
void setup() {
  Serial.begin(115200); // Use a faster baud rate
  Serial.println();
  Serial.println("=======================================");
  Serial.print("Starting NodeMCU Sensor v"); Serial.println(FIRMWARE_VERSION);
  Serial.println("=======================================");

  Wire.begin(SDA_PIN, SCL_PIN); // Initialize I2C

  setupDisplay(); // Initialize OLED Display first for feedback
  setupSensor(); // Initialize AM2320 Sensor
  loadGeneralConfig(); // Load settings like calibration, interval, name
  setupWiFi(); // Initialize WiFi (connects or starts AP)

  // Only proceed with MQTT setup if WiFi connected successfully
  if (WiFi.status() == WL_CONNECTED) {
    setupMQTT(); // Setup MQTT topics and server details
  } else {
    Serial.println("Skipping MQTT setup - WiFi not connected.");
  }

  Serial.println("Setup Complete. Starting main loop...");
}

// === MAIN LOOP ===
void loop() {
  // Handle Web Server and DNS requests only when in AP Config Mode
  if (configModeActive) {
    handleDns();
    handleWebServer();
    // Optionally update display to show AP mode status periodically
    // No sensor reads or MQTT handling in this mode
    return; // Exit loop early if in config mode
  }

  // --- Normal Operation Mode ---

  // 1. Ensure WiFi is connected (ESP core handles auto-reconnect)
  //    Could add a check here to reboot if WiFi remains disconnected for too long

  // 2. Ensure MQTT is connected
  if (WiFi.status() == WL_CONNECTED) {
      connectMQTT(); // Attempt to connect/reconnect if needed
  }

  // 3. Process incoming MQTT messages
  mqttClient.loop(); // Must be called regularly

  // 4. Read sensor data periodically
  updateSensorReadings();

  // 5. Update the display periodically
  updateDisplay();

  // 6. Publish data to MQTT periodically
  unsigned long now = millis();
  if (mqttClient.connected() && (now - lastPublishTimestamp >= publishIntervalMs)) {
      // Note: lastPublishTimestamp is updated inside publishSensorData on success
      publishSensorData();
  }

  // Yield for ESP8266 background tasks (important!)
  yield();
}