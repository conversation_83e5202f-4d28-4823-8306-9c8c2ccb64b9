; PlatformIO Project Configuration File for AWS IoT Sensor
;
; ESP8266 NodeMCU v1 with AM2320 sensor, OLED display, and AWS IoT Core connectivity
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:nodemcu]
platform = espressif8266
board = nodemcu
framework = arduino
monitor_speed = 115200
upload_port = COM11
upload_speed = 115200
upload_resetmethod = nodemcu

lib_deps =
  ; AWS IoT Device SDK
  256dpi/MQTT@^2.5.0
  ; Sensor and Display Libraries
  adafruit/Adafruit Unified Sensor@^1.1.9
  adafruit/Adafruit AM2320 sensor library@^1.2.5
  adafruit/Adafruit SSD1306@^2.5.7
  adafruit/Adafruit GFX Library@^1.11.9
  ; JSON and WiFi
  bblanchon/ArduinoJson@^6.21.2
  ; Time library for AWS timestamps
  paulstoffregen/Time@^1.6.1
  ; WiFi Manager for easy configuration
  tzapu/WiFiManager@^0.16.0

board_build.flash_mode = dio

; Build flags for AWS IoT
build_flags = 
  -DAWS_IOT_MQTT_HOST=\"your-aws-iot-endpoint.iot.region.amazonaws.com\"
  -DAWS_IOT_MQTT_PORT=8883
  -DDEVICE_TYPE=101
