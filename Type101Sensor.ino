// NodeMCU12_MQTT_AM2320_OLED_v0.7 (Reviewed & Optimized)
// - Improved organization and comments
// - Added minor robustness checks (MQTT publish)
// - Consistent use of const for pins
// - Minor code style adjustments

// === LIBRARIES ===
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <DNSServer.h>
#include <PubSubClient.h>
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_AM2320.h>       // AM2320 Temp/Humidity Sensor
#include <Adafruit_SSD1306.h>     // OLED Display Driver
#include <Adafruit_GFX.h>         // Graphics library for display
#include <ArduinoJson.h>          // MQTT Payload Handling
#include <EEPROM.h>               // Configuration Storage

// === ICONS (Embedded) ===
// WiFi Icons (8 bytes each)
const uint8_t wifi_icon_0[] PROGMEM = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}; // No WiFi
const uint8_t wifi_icon_1[] PROGMEM = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00}; // 1 bar
const uint8_t wifi_icon_2[] PROGMEM = {0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x08, 0x00}; // 2 bars
const uint8_t wifi_icon_3[] PROGMEM = {0x00, 0x00, 0x00, 0x3E, 0x00, 0x1C, 0x08, 0x00}; // 3 bars
const uint8_t wifi_icon_4[] PROGMEM = {0x00, 0x7F, 0x00, 0x3E, 0x00, 0x1C, 0x08, 0x00}; // 4 bars

// MQTT Icons (8 bytes each)
const uint8_t mqtt_connected[] PROGMEM = {0x3C, 0x42, 0x81, 0x81, 0x81, 0x81, 0x42, 0x3C}; // Circle (connected)
const uint8_t mqtt_disconnected[] PROGMEM = {0x3C, 0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x3C}; // Filled circle (disconnected)

// === FIRMWARE & DEVICE INFO ===
const char* FIRMWARE_VERSION = "v0.7";
const int DEVICE_TYPE = 101; // Unique identifier for this *type* of device

// === PIN CONFIGURATION ===
const int SDA_PIN = D2; // I2C Data for Sensor/Display
const int SCL_PIN = D1; // I2C Clock for Sensor/Display
// Note: A0 is used implicitly by analogRead(A0)

// === EEPROM CONFIGURATION ===
// Using EEPROM to store persistent configuration data
#define EEPROM_SIZE 512 // Increased size slightly for safety, adjust if needed

// EEPROM Addresses (ensure no overlaps)
#define EEPROM_ADDR_ADC_CAL          0  // Float (4 bytes)
#define EEPROM_ADDR_TEMP_CAL         4  // Float (4 bytes)
#define EEPROM_ADDR_HUMID_CAL        8  // Float (4 bytes)
#define EEPROM_ADDR_NAME             12 // String (up to FRIENDLY_NAME_MAXLEN bytes)
#define FRIENDLY_NAME_MAXLEN         48 // Max length for friendly name (incl. null)
#define EEPROM_ADDR_PUBLISH_INTERVAL (EEPROM_ADDR_NAME + FRIENDLY_NAME_MAXLEN) // Unsigned Long (4 bytes) ~60
// --- Gap ---
#define EEPROM_ADDR_WIFI_SSID        128 // String (up to WIFI_CRED_MAXLEN bytes)
#define WIFI_CRED_MAXLEN             64  // Max length for SSID/Password (incl. null)
#define EEPROM_ADDR_WIFI_PASS        (EEPROM_ADDR_WIFI_SSID + WIFI_CRED_MAXLEN) // String (up to WIFI_CRED_MAXLEN bytes) ~192

// === WIFI CONFIGURATION ===
#define WIFI_RETRY_LIMIT 30        // How many 500ms attempts before AP mode
#define WIFI_CONNECT_TIMEOUT_MS (WIFI_RETRY_LIMIT * 500)
const char* DEFAULT_WIFI_SSID = "HaciendaHagansLR"; // Default if EEPROM is empty
const char* DEFAULT_WIFI_PASS = "F1nglongers";    // Default if EEPROM is empty
char wifi_ssid[WIFI_CRED_MAXLEN];                 // Buffer for current SSID
char wifi_pass[WIFI_CRED_MAXLEN];                 // Buffer for current Password

// === MQTT CONFIGURATION ===
const char* MQTT_SERVER = "45.32.221.98";
const int MQTT_PORT = 1883;
const char* MQTT_USER = "sensoruser";
const char* MQTT_PASS = "farklebobstein69";
// MQTT Topics will be constructed in setup() based on NodeID and metadata

// === MQTT METADATA (Used for Topic Construction & Payload) ===
// Consider making some of these configurable via MQTT/EEPROM if needed
const char* VENDOR = "mikehagans";
const int VENDOR_ID = 0; // Example ID
const char* ORG = "Hacienda";
const int ORG_ID = 100; // Example ID
const char* AGGREGATOR = "na"; // Example
const char* SITE = "barn";
const char* SITE_ID = "2"; // Example ID
const char* NODE_LOCATION = "DeepFreezer1"; // Physical location/group served by this node

// === SENSOR CONFIGURATION ===
Adafruit_AM2320 am2320 = Adafruit_AM2320();
#define SENSOR_READ_INTERVAL_MS 2000 // How often to read the sensor (AM2320 needs >1.5s between reads)

// === DISPLAY CONFIGURATION ===
#define SCREEN_WIDTH 128 // OLED display width, in pixels
#define SCREEN_HEIGHT 32 // OLED display height, in pixels
#define OLED_RESET_PIN -1 // Reset pin # (or -1 if sharing Arduino reset pin)
#define DISPLAY_UPDATE_INTERVAL_MS 1000 // How often to refresh the display

// === TIMING & INTERVALS ===
#define PUBLISH_INTERVAL_DEFAULT_MS 30000 // Default MQTT publish interval if not set
#define MQTT_RECONNECT_INTERVAL_MS 5000 // Wait 5s between MQTT reconnect attempts

// === OBJECTS ===
WiFiClient espClient;
PubSubClient mqttClient(espClient);
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET_PIN);
ESP8266WebServer webServer(80);
DNSServer dnsServer;

// === GLOBAL VARIABLES ===
bool displayOK = false;           // Was the display initialized successfully?
bool configModeActive = false;    // Is the device in AP Config Portal mode?
float adcCalibration = 1.00;      // Calibration factor for ADC reading
float sensorCalOffsetTemp = 0.0;  // Calibration offset for temperature
float sensorCalOffsetHumidity = 0.0;// Calibration offset for humidity
unsigned long publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS; // Current MQTT publish interval
char friendlyName[FRIENDLY_NAME_MAXLEN] = "rackroom"; // User-friendly name for the device
char nodeID[7];                   // Unique 6-char Base62 Node ID from ChipID + Null terminator

// MQTT Topic Strings (will be populated in setup)
String publishTopic;
String configTopic;
String statusTopic; // Optional: For online/offline status

// State variables for timing and last known values
unsigned long lastPublishTimestamp = 0;
unsigned long lastDisplayUpdateTimestamp = 0;
unsigned long lastSensorUpdateTimestamp = 0;
unsigned long lastMqttAttemptTimestamp = 0;
float lastTempC = NAN;            // Use NAN to indicate no valid reading yet
float lastHumidity = NAN;         // Use NAN to indicate no valid reading yet
int lastADCPercent = -1;          // Use -1 to indicate no valid reading yet

// === FUNCTION PROTOTYPES ===
// --- Configuration (EEPROM) ---
void loadGeneralConfig();
void saveGeneralConfig();
void loadWiFiCredentials();
void saveWiFiCredentials(const String& newSSID, const String& newPASS);
// --- WiFi & Network ---
void setupWiFi();
void startConfigPortal();
void handleWebServer();
void handleDns();
// --- MQTT ---
void setupMQTT();
void connectMQTT();
void mqttCallback(char* topic, byte* payload, unsigned int length);
void applyConfigPayload(const char* payload);
void publishSensorData();
void publishStatus(const char* status); // Optional: Publish online/offline
// --- Sensor ---
void setupSensor();
void updateSensorReadings();
// --- Display ---
void setupDisplay();
void updateDisplay();
void drawTopStatusBar();
void drawPublishProgressBar();
const uint8_t* getWifiIcon(int level, uint8_t &width);
int wifiLevelFromRSSI(long rssi);
// --- Utilities ---
void generateNodeID();
void base62Encode(uint32_t value, char *output, int length);

// === UTILITIES ===
const char base62Chars[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

void base62Encode(uint32_t value, char *output, int length) {
  for (int i = length - 1; i >= 0; --i) {
    output[i] = base62Chars[value % 62];
    value /= 62;
  }
  output[length] = '\0'; // Null-terminate the string
}

void generateNodeID() {
  uint32_t chipId = ESP.getChipId();
  base62Encode(chipId, nodeID, 6); // Generate 6-character base62 ID
  Serial.print("Generated NodeID: ");
  Serial.println(nodeID);
}

// === EEPROM FUNCTIONS ===
void loadWiFiCredentials() {
  Serial.println("Loading WiFi credentials from EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  bool credsValid = true;
  // Read SSID
  for (int i = 0; i < WIFI_CRED_MAXLEN; i++) {
    wifi_ssid[i] = EEPROM.read(EEPROM_ADDR_WIFI_SSID + i);
    if (i == 0 && wifi_ssid[i] == 0xFF) { // Check if EEPROM seems uninitialized
        credsValid = false;
        break;
    }
    if (wifi_ssid[i] == '\0') break; // Stop at null terminator
  }
  wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination

  // Read Password only if SSID seemed valid
  if (credsValid) {
      for (int i = 0; i < WIFI_CRED_MAXLEN; i++) {
        wifi_pass[i] = EEPROM.read(EEPROM_ADDR_WIFI_PASS + i);
        if (wifi_pass[i] == '\0') break; // Stop at null terminator
      }
      wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
  }

  EEPROM.end();

  // If EEPROM was empty/invalid or strings are zero length, use defaults
  if (!credsValid || strlen(wifi_ssid) == 0 ) { // Don't require password to be present
    Serial.println("EEPROM WiFi credentials invalid or empty, using defaults.");
    strncpy(wifi_ssid, DEFAULT_WIFI_SSID, WIFI_CRED_MAXLEN -1);
    strncpy(wifi_pass, DEFAULT_WIFI_PASS, WIFI_CRED_MAXLEN -1);
    wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
    wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0';
  } else {
      Serial.println("WiFi credentials loaded successfully.");
  }

  Serial.print("SSID: "); Serial.println(wifi_ssid);
  // Avoid printing password to Serial for security, uncomment if needed for debug
  // Serial.print("Password: "); Serial.println(wifi_pass);
}

void saveWiFiCredentials(const String& newSSID, const String& newPASS) {
  Serial.println("Saving WiFi credentials to EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  // Clear old SSID area first (optional, but good practice)
  for (int i = 0; i < WIFI_CRED_MAXLEN; i++) EEPROM.write(EEPROM_ADDR_WIFI_SSID + i, 0);
  // Write new SSID
  for (unsigned int i = 0; i < newSSID.length() && i < (unsigned int)(WIFI_CRED_MAXLEN -1); i++) {
    EEPROM.write(EEPROM_ADDR_WIFI_SSID + i, newSSID[i]);
  }
  // Clear old Password area
  for (unsigned int i = 0; i < WIFI_CRED_MAXLEN; i++) EEPROM.write(EEPROM_ADDR_WIFI_PASS + i, 0);
  // Write new Password
  for (unsigned int i = 0; i < newPASS.length() && i < (unsigned int)(WIFI_CRED_MAXLEN - 1); i++) {
    EEPROM.write(EEPROM_ADDR_WIFI_PASS + i, newPASS[i]);
  }

  if (EEPROM.commit()) {
    Serial.println("EEPROM WiFi credentials saved successfully.");
  } else {
    Serial.println("ERROR: EEPROM commit failed!");
  }
  EEPROM.end();

  // Update in-memory buffers immediately
  strncpy(wifi_ssid, newSSID.c_str(), WIFI_CRED_MAXLEN -1);
  strncpy(wifi_pass, newPASS.c_str(), WIFI_CRED_MAXLEN -1);
   wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
   wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0';
}

// === SETUP FUNCTION ===
void setup() {
  Serial.begin(115200);
  Serial.println();
  Serial.println("=== NodeMCU Sensor Starting ===");

  // Initialize I2C with custom pins
  Wire.begin(SDA_PIN, SCL_PIN);

  // Initialize display
  if(display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
    displayOK = true;
    Serial.println("Display initialized successfully");
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("Starting...");
    display.display();
  } else {
    Serial.println("Display initialization failed");
  }

  // Load configuration
  loadGeneralConfig();

  // Initialize sensor
  Serial.println("Initializing AM2320 sensor...");
  am2320.begin();

  // Setup WiFi
  setupWiFi();

  // Setup MQTT if WiFi connected
  if (WiFi.status() == WL_CONNECTED) {
    setupMQTT();
  }

  Serial.println("Setup complete!");
}

// === MAIN LOOP ===
void loop() {
  // Handle WiFi config portal if active
  if (configModeActive) {
    handleWebServer();
    handleDns();
    return; // Skip normal operation in config mode
  }

  // Ensure WiFi is connected
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi disconnected, attempting reconnect...");
    setupWiFi();
    return;
  }

  // Ensure MQTT is connected
  connectMQTT();
  mqttClient.loop(); // Process MQTT messages

  // Update sensor readings
  updateSensorReadings();

  // Update display
  updateDisplay();

  // Publish data at intervals
  unsigned long now = millis();
  if (now - lastPublishTimestamp >= publishIntervalMs) {
    publishSensorData();
  }

  delay(100); // Small delay to prevent overwhelming the loop
}

// === MISSING ESSENTIAL FUNCTIONS ===

void loadGeneralConfig() {
  Serial.println("Loading general config from EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  EEPROM.get(EEPROM_ADDR_ADC_CAL, adcCalibration);
  EEPROM.get(EEPROM_ADDR_TEMP_CAL, sensorCalOffsetTemp);
  EEPROM.get(EEPROM_ADDR_HUMID_CAL, sensorCalOffsetHumidity);
  EEPROM.get(EEPROM_ADDR_PUBLISH_INTERVAL, publishIntervalMs);

  // Validate loaded values
  if (isnan(adcCalibration)) adcCalibration = 1.0;
  if (isnan(sensorCalOffsetTemp)) sensorCalOffsetTemp = 0.0;
  if (isnan(sensorCalOffsetHumidity)) sensorCalOffsetHumidity = 0.0;
  if (publishIntervalMs < 5000 || publishIntervalMs > 3600000) {
    publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS;
  }

  // Read friendly name
  for (int i = 0; i < FRIENDLY_NAME_MAXLEN; i++) {
    friendlyName[i] = EEPROM.read(EEPROM_ADDR_NAME + i);
    if (friendlyName[i] == '\0') break;
  }
  friendlyName[FRIENDLY_NAME_MAXLEN - 1] = '\0';
  if (strlen(friendlyName) == 0) {
    strcpy(friendlyName, "Default Sensor");
  }
  EEPROM.end();
}

void saveGeneralConfig() {
  EEPROM.begin(EEPROM_SIZE);
  EEPROM.put(EEPROM_ADDR_ADC_CAL, adcCalibration);
  EEPROM.put(EEPROM_ADDR_TEMP_CAL, sensorCalOffsetTemp);
  EEPROM.put(EEPROM_ADDR_HUMID_CAL, sensorCalOffsetHumidity);
  EEPROM.put(EEPROM_ADDR_PUBLISH_INTERVAL, publishIntervalMs);

  for (int i = 0; i < FRIENDLY_NAME_MAXLEN - 1; i++) {
    EEPROM.write(EEPROM_ADDR_NAME + i, friendlyName[i]);
    if (friendlyName[i] == '\0') break;
  }
  EEPROM.commit();
  EEPROM.end();
}

void setupWiFi() {
  generateNodeID();
  loadWiFiCredentials();

  WiFi.mode(WIFI_STA);
  WiFi.hostname(nodeID);

  if (displayOK) {
    display.clearDisplay();
    display.setCursor(0, 0);
    display.println("Connecting WiFi:");
    display.setCursor(0, 10);
    display.println(wifi_ssid);
    display.display();
  }

  WiFi.begin(wifi_ssid, wifi_pass);
  WiFi.setAutoReconnect(true);

  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && (millis() - startTime < WIFI_CONNECT_TIMEOUT_MS)) {
    delay(500);
    Serial.print(".");
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi Connected!");
    Serial.print("IP: "); Serial.println(WiFi.localIP());
  } else {
    Serial.println("WiFi Failed - Starting AP Mode");
    startConfigPortal();
  }
}

void startConfigPortal() {
  configModeActive = true;
  WiFi.mode(WIFI_AP);
  String apName = "SensorSetup_" + String(nodeID);

  if(WiFi.softAP(apName.c_str())) {
    dnsServer.start(53, "*", WiFi.softAPIP());

    webServer.on("/", HTTP_GET, []() {
      String html = R"(
        <html><head><title>Sensor WiFi Setup</title>
        <style>body{font-family: sans-serif; margin: 20px;}
               input{width: 90%; padding: 8px; margin-bottom: 10px;}
        </style>
        </head><body>
        <h2>Configure WiFi</h2>
        <form action='/save' method='POST'>
          <label>SSID:</label><input name='ssid' required><br>
          <label>Password:</label><input name='pass' type='password'><br><br>
          <input type='submit' value='Save & Reboot'>
        </form></body></html>)";
      webServer.send(200, "text/html", html);
    });

    webServer.on("/save", HTTP_POST, []() {
      saveWiFiCredentials(webServer.arg("ssid"), webServer.arg("pass"));
      webServer.send(200, "text/html", "<h2>Saved! Rebooting...</h2>");
      delay(2000);
      ESP.restart();
    });

    webServer.onNotFound([]() {
      webServer.sendHeader("Location", "/", true);
      webServer.send(302, "text/plain", "");
    });

    webServer.begin();

    if (displayOK) {
      display.clearDisplay();
      display.setCursor(0, 0);
      display.println("CONFIG MODE");
      display.setCursor(0, 10);
      display.print("AP: ");
      display.println(apName);
      display.display();
    }
  }
}

void handleWebServer() {
  if(configModeActive) webServer.handleClient();
}

void handleDns() {
  if(configModeActive) dnsServer.processNextRequest();
}

void setupMQTT() {
  String baseTopic = String(VENDOR) + "/" + String(ORG) + "/" + String(SITE) + "/" + String(NODE_LOCATION) + "/" + String(nodeID);
  publishTopic = "sensors/" + baseTopic + "/data";
  configTopic  = "config/" + baseTopic + "/set";
  statusTopic  = "status/" + baseTopic + "/state";

  mqttClient.setServer(MQTT_SERVER, MQTT_PORT);
  mqttClient.setCallback(mqttCallback);
}

void connectMQTT() {
  if (!mqttClient.connected()) {
    unsigned long now = millis();
    if (now - lastMqttAttemptTimestamp > MQTT_RECONNECT_INTERVAL_MS) {
      lastMqttAttemptTimestamp = now;
      String clientID = "esp8266-" + String(nodeID);

      if (mqttClient.connect(clientID.c_str(), MQTT_USER, MQTT_PASS, statusTopic.c_str(), 0, true, "offline")) {
        Serial.println("MQTT Connected!");
        publishStatus("online");
        mqttClient.subscribe(configTopic.c_str());
      } else {
        Serial.print("MQTT failed, rc="); Serial.println(mqttClient.state());
      }
    }
  }
}

void mqttCallback(char* topic, byte* payload, unsigned int length) {
  char msg[length + 1];
  memcpy(msg, payload, length);
  msg[length] = '\0';
  Serial.print("MQTT message: "); Serial.println(msg);

  if (String(topic) == configTopic) {
    applyConfigPayload(msg);
  }
}

void applyConfigPayload(const char* payload) {
  StaticJsonDocument<256> doc;
  if (deserializeJson(doc, payload) == DeserializationError::Ok) {
    bool changed = false;

    if (doc.containsKey("adc_calibration")) {
      adcCalibration = doc["adc_calibration"];
      changed = true;
    }
    if (doc.containsKey("temp_offset")) {
      sensorCalOffsetTemp = doc["temp_offset"];
      changed = true;
    }
    if (doc.containsKey("humidity_offset")) {
      sensorCalOffsetHumidity = doc["humidity_offset"];
      changed = true;
    }
    if (doc.containsKey("publish_interval_ms")) {
      unsigned long val = doc["publish_interval_ms"];
      if (val >= 5000 && val <= 3600000) {
        publishIntervalMs = val;
        changed = true;
      }
    }

    if (changed) {
      saveGeneralConfig();
      Serial.println("Configuration updated");
    }
  }
}

void publishSensorData() {
  if (isnan(lastTempC) || isnan(lastHumidity) || !mqttClient.connected()) {
    return;
  }

  StaticJsonDocument<512> doc;
  doc["device_type"] = DEVICE_TYPE;
  doc["node_id"] = nodeID;
  doc["fw_version"] = FIRMWARE_VERSION;
  doc["friendly_name"] = friendlyName;
  doc["timestamp"] = millis();
  doc["rssi"] = WiFi.RSSI();

  JsonObject values = doc.createNestedObject("values");
  values["temperature_c"] = lastTempC + sensorCalOffsetTemp;
  values["humidity_percent"] = lastHumidity + sensorCalOffsetHumidity;
  values["adc_percent"] = lastADCPercent;

  char buffer[512];
  serializeJson(doc, buffer);

  if (mqttClient.publish(publishTopic.c_str(), buffer)) {
    Serial.println("MQTT Published");
    lastPublishTimestamp = millis();
  }
}

void publishStatus(const char* status) {
  if (mqttClient.connected()) {
    mqttClient.publish(statusTopic.c_str(), status, true);
  }
}

void updateSensorReadings() {
  unsigned long now = millis();
  if (now - lastSensorUpdateTimestamp >= SENSOR_READ_INTERVAL_MS) {
    lastSensorUpdateTimestamp = now;

    float tempC = am2320.readTemperature();
    float humidity = am2320.readHumidity();
    int adcRaw = analogRead(A0);
    lastADCPercent = constrain((int)(adcRaw / 1023.0 * 100.0 * adcCalibration), 0, 100);

    if (!isnan(tempC) && !isnan(humidity)) {
      lastTempC = tempC;
      lastHumidity = humidity;
      Serial.print("Temp: "); Serial.print(lastTempC);
      Serial.print("°C, Humidity: "); Serial.print(lastHumidity);
      Serial.print("%, ADC: "); Serial.println(lastADCPercent);
    }
  }
}

void updateDisplay() {
  unsigned long now = millis();
  if (now - lastDisplayUpdateTimestamp >= DISPLAY_UPDATE_INTERVAL_MS && displayOK) {
    lastDisplayUpdateTimestamp = now;

    display.clearDisplay();
    display.setTextSize(1);
    display.setCursor(0, 0);

    if (configModeActive) {
      display.println("CONFIG MODE");
      display.print("AP: SensorSetup_");
      display.println(nodeID);
    } else if (WiFi.status() == WL_CONNECTED) {
      display.print("WiFi: "); display.println(WiFi.RSSI());
      display.print("MQTT: "); display.println(mqttClient.connected() ? "OK" : "NO");

      if (!isnan(lastTempC)) {
        display.print("T:"); display.print(lastTempC, 1); display.print("C ");
        display.print("H:"); display.print(lastHumidity, 0); display.println("%");
        display.print("ADC: "); display.print(lastADCPercent); display.println("%");
      } else {
        display.println("Sensor: ERROR");
      }
    } else {
      display.println("WiFi: Disconnected");
    }

    display.display();
  }
}
